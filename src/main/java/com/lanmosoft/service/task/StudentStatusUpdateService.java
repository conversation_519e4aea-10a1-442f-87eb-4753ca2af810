package com.lanmosoft.service.task;

import com.lanmosoft.dao.model.*;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.service.lanmobase.SequenceManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学生状态更新服务
 * 专门处理学生上课状态的批量更新逻辑
 */
@Service
public class StudentStatusUpdateService {

    @Autowired
    private Student_zoneService student_zoneService;
    
    @Autowired
    private ContractService contractService;
    
    @Autowired
    private Consumption_studentService consumption_studentService;
    
    @Autowired
    private View_consumption_student_contractService view_consumption_student_contractService;
    
    @Autowired
    private OperationLogService operationLogService;
    
    @Autowired
    private SequenceManager sequenceManager;

    /**
     * 批量更新学生上课状态 - 优化版本
     */
    public void updateStudentClassStatusBatch() {
        long startTime = System.currentTimeMillis();
        System.out.println("开始执行学员上课状态更新任务...");
        
        try {
            // 1. 批量查询所有需要更新的学生
            WhereCondition wc = new WhereCondition();
            wc.andNotEquals("classStatus","5");
            List<Student_zone> allStudents = student_zoneService.query(wc);
            
            if (allStudents.isEmpty()) {
                System.out.println("没有需要更新状态的学生");
                return;
            }
            
            System.out.println("需要处理的学生数量: " + allStudents.size());
            
            // 2. 批量查询所有相关数据
            Map<String, List<Contract>> executeContractsMap = batchQueryExecuteContracts(allStudents);
            Map<String, List<Contract>> confirmedContractsMap = batchQueryConfirmedContracts(allStudents);
            Map<String, List<Consumption_student>> consumptionStudentsMap = batchQueryConsumptionStudents(allStudents);
            Map<String, List<View_consumption_student_contract>> consumptionContractsMap = 
                    batchQueryConsumptionContractsOptimized(allStudents, consumptionStudentsMap);
            
            // 3. 批量处理状态更新
            List<Student_zone> studentsToUpdate = new ArrayList<>();
            
            for (Student_zone sz : allStudents) {
                String studentId = sz.getStudent_id();
                String newStatus = determineClassStatus(sz, executeContractsMap.get(studentId), 
                        confirmedContractsMap.get(studentId), consumptionStudentsMap.get(studentId), 
                        consumptionContractsMap.get(studentId));
                
                if (!newStatus.equals(sz.getClassStatus())) {
                    sz.setClassStatus(newStatus);
                    studentsToUpdate.add(sz);
                }
            }
            
            // 4. 批量更新数据库
            if (!studentsToUpdate.isEmpty()) {
                System.out.println("需要更新状态的学生数量: " + studentsToUpdate.size());
                batchUpdateStudentStatus(studentsToUpdate);
            } else {
                System.out.println("所有学生状态都是最新的，无需更新");
            }
            
            long endTime = System.currentTimeMillis();
            System.out.println("学员上课状态更新任务完成，耗时: " + (endTime - startTime) + "ms");
            
            // 记录操作日志
            logOperation("定时任务,更新学员上课状态,耗时:" + (endTime - startTime) + "ms");
            
        } catch (Exception e) {
            System.err.println("学员上课状态更新任务执行失败: " + e.getMessage());
            e.printStackTrace();
            
            // 记录错误日志
            logOperation("定时任务,更新学员上课状态失败:" + e.getMessage());
            throw e;
        }
    }

    /**
     * 批量查询执行中的合同
     */
    private Map<String, List<Contract>> batchQueryExecuteContracts(List<Student_zone> students) {
        Map<String, List<Contract>> result = new HashMap<>();
        
        // 按BUType分组查询以提高效率
        Map<String, List<String>> studentsByBUType = new HashMap<>();
        for (Student_zone sz : students) {
            studentsByBUType.computeIfAbsent(sz.getBUType(), k -> new ArrayList<>()).add(sz.getStudent_id());
        }
        
        for (Map.Entry<String, List<String>> entry : studentsByBUType.entrySet()) {
            String buType = entry.getKey();
            List<String> studentIds = entry.getValue();
            
            if (studentIds.size() > 100) {
                // 如果学生数量太多，分批查询
                for (int i = 0; i < studentIds.size(); i += 100) {
                    List<String> batch = studentIds.subList(i, Math.min(i + 100, studentIds.size()));
                    queryExecuteContractsBatch(batch, buType, result);
                }
            } else {
                queryExecuteContractsBatch(studentIds, buType, result);
            }
        }
        
        return result;
    }
    
    /**
     * 查询执行中合同的批次处理
     */
    private void queryExecuteContractsBatch(List<String> studentIds, String buType, Map<String, List<Contract>> result) {
        WhereCondition wc = new WhereCondition();
        wc.andEquals("status", "2");
        wc.andIn("studentId", studentIds);
        addContractType2(buType, wc);
        
        List<Contract> contracts = contractService.query(wc);
        
        // 按学生ID分组
        for (Contract contract : contracts) {
            result.computeIfAbsent(contract.getStudentId(), k -> new ArrayList<>()).add(contract);
        }
        
        // 确保所有学生都有对应的条目（即使是空列表）
        for (String studentId : studentIds) {
            result.computeIfAbsent(studentId, k -> new ArrayList<>());
        }
    }

    /**
     * 批量查询待确认的合同
     */
    private Map<String, List<Contract>> batchQueryConfirmedContracts(List<Student_zone> students) {
        Map<String, List<Contract>> result = new HashMap<>();
        
        // 按BUType分组查询
        Map<String, List<String>> studentsByBUType = new HashMap<>();
        for (Student_zone sz : students) {
            studentsByBUType.computeIfAbsent(sz.getBUType(), k -> new ArrayList<>()).add(sz.getStudent_id());
        }
        
        for (Map.Entry<String, List<String>> entry : studentsByBUType.entrySet()) {
            String buType = entry.getKey();
            List<String> studentIds = entry.getValue();
            
            if (studentIds.size() > 100) {
                for (int i = 0; i < studentIds.size(); i += 100) {
                    List<String> batch = studentIds.subList(i, Math.min(i + 100, studentIds.size()));
                    queryConfirmedContractsBatch(batch, buType, result);
                }
            } else {
                queryConfirmedContractsBatch(studentIds, buType, result);
            }
        }
        
        return result;
    }
    
    /**
     * 查询待确认合同的批次处理
     */
    private void queryConfirmedContractsBatch(List<String> studentIds, String buType, Map<String, List<Contract>> result) {
        WhereCondition wc = new WhereCondition();
        wc.andEquals("status", "1");
        wc.andIn("studentId", studentIds);
        addContractType2(buType, wc);
        
        List<Contract> contracts = contractService.query(wc);
        
        // 按学生ID分组
        for (Contract contract : contracts) {
            result.computeIfAbsent(contract.getStudentId(), k -> new ArrayList<>()).add(contract);
        }
        
        // 确保所有学生都有对应的条目
        for (String studentId : studentIds) {
            result.computeIfAbsent(studentId, k -> new ArrayList<>());
        }
    }

    /**
     * 批量查询学生消费记录
     */
    private Map<String, List<Consumption_student>> batchQueryConsumptionStudents(List<Student_zone> students) {
        Map<String, List<Consumption_student>> result = new HashMap<>();
        
        List<String> studentIds = students.stream()
                .map(Student_zone::getStudent_id)
                .collect(Collectors.toList());
        
        if (studentIds.size() > 100) {
            for (int i = 0; i < studentIds.size(); i += 100) {
                List<String> batch = studentIds.subList(i, Math.min(i + 100, studentIds.size()));
                queryConsumptionStudentsBatch(batch, result);
            }
        } else {
            queryConsumptionStudentsBatch(studentIds, result);
        }
        
        return result;
    }
    
    /**
     * 查询学生消费记录的批次处理
     */
    private void queryConsumptionStudentsBatch(List<String> studentIds, Map<String, List<Consumption_student>> result) {
        WhereCondition wc = new WhereCondition();
        wc.andIn("student_id", studentIds);
        
        List<Consumption_student> consumptions = consumption_studentService.query(wc);
        
        // 按学生ID分组
        for (Consumption_student consumption : consumptions) {
            result.computeIfAbsent(consumption.getStudent_id(), k -> new ArrayList<>()).add(consumption);
        }
        
        // 确保所有学生都有对应的条目
        for (String studentId : studentIds) {
            result.computeIfAbsent(studentId, k -> new ArrayList<>());
        }
    }

    /**
     * 判断合同类型,添加对应的条件
     */
    private void addContractType2(String BUType, WhereCondition wc) {
        List<String> contractType2List = new ArrayList<>();
        if(Enums.OFFlINE.equals(BUType)){
            contractType2List.add(Enums.OFFlINE);
            contractType2List.add(Enums.ContractType2.HYBRID);
            wc.andIn("contractType2", contractType2List);
        }
        if(Enums.ONLINE.equals(BUType)){
            contractType2List.add(Enums.ONLINE);
            contractType2List.add(Enums.ContractType2.HYBRID);
            wc.andIn("contractType2", contractType2List);
        }
    }

    /**
     * 优化的批量查询消费学生合同视图 - 避免重复查询
     */
    private Map<String, List<View_consumption_student_contract>> batchQueryConsumptionContractsOptimized(
            List<Student_zone> students, Map<String, List<Consumption_student>> consumptionStudentsMap) {
        Map<String, List<View_consumption_student_contract>> result = new HashMap<>();

        // 收集所有消费记录ID
        List<String> allConsumptionIds = new ArrayList<>();
        Map<String, String> consumptionToStudentMap = new HashMap<>();

        for (Student_zone sz : students) {
            List<Consumption_student> consumptions = consumptionStudentsMap.get(sz.getStudent_id());
            if (consumptions != null) {
                for (Consumption_student cs : consumptions) {
                    allConsumptionIds.add(cs.getId());
                    consumptionToStudentMap.put(cs.getId(), sz.getStudent_id());
                }
            }
        }

        if (!allConsumptionIds.isEmpty()) {
            if (allConsumptionIds.size() > 100) {
                for (int i = 0; i < allConsumptionIds.size(); i += 100) {
                    List<String> batch = allConsumptionIds.subList(i, Math.min(i + 100, allConsumptionIds.size()));
                    queryConsumptionContractsBatch(batch, consumptionToStudentMap, result);
                }
            } else {
                queryConsumptionContractsBatch(allConsumptionIds, consumptionToStudentMap, result);
            }
        }

        // 确保所有学生都有对应的条目
        for (Student_zone sz : students) {
            result.computeIfAbsent(sz.getStudent_id(), k -> new ArrayList<>());
        }

        return result;
    }

    /**
     * 查询消费合同视图的批次处理
     */
    private void queryConsumptionContractsBatch(List<String> consumptionIds,
            Map<String, String> consumptionToStudentMap,
            Map<String, List<View_consumption_student_contract>> result) {

        WhereCondition wc = new WhereCondition();
        wc.andIn("consumption_student_id", consumptionIds);
        wc.andIsNull("contract_id");

        List<View_consumption_student_contract> contracts = view_consumption_student_contractService.query(wc);

        // 按学生ID分组
        for (View_consumption_student_contract contract : contracts) {
            String studentId = consumptionToStudentMap.get(contract.getConsumption_student_id());
            if (studentId != null) {
                result.computeIfAbsent(studentId, k -> new ArrayList<>()).add(contract);
            }
        }
    }

    /**
     * 确定学生的上课状态
     */
    private String determineClassStatus(Student_zone sz,
            List<Contract> executeContracts,
            List<Contract> confirmedContracts,
            List<Consumption_student> consumptionStudents,
            List<View_consumption_student_contract> consumptionContracts) {

        // 如果有执行中的合同，状态为"3"
        if (executeContracts != null && !executeContracts.isEmpty()) {
            return "3";
        }

        // 如果有待确认的合同，状态为"2"
        if (confirmedContracts != null && !confirmedContracts.isEmpty()) {
            return "2";
        }

        // 检查消费记录
        if (consumptionStudents != null && !consumptionStudents.isEmpty()) {
            boolean hasMatchingBUType = false;

            if (consumptionContracts != null) {
                for (View_consumption_student_contract vcsc : consumptionContracts) {
                    if (sz.getBUType().equals(vcsc.getBUType())) {
                        hasMatchingBUType = true;
                        break;
                    }
                }
            }

            return hasMatchingBUType ? "1" : "4";
        }

        // 默认保持原状态
        return sz.getClassStatus();
    }

    /**
     * 批量更新学生状态
     */
    private void batchUpdateStudentStatus(List<Student_zone> studentsToUpdate) {
        // 分批更新，每批50个
        int batchSize = 50;
        for (int i = 0; i < studentsToUpdate.size(); i += batchSize) {
            List<Student_zone> batch = studentsToUpdate.subList(i, Math.min(i + batchSize, studentsToUpdate.size()));

            for (Student_zone sz : batch) {
                try {
                    student_zoneService.update(sz);
                } catch (Exception e) {
                    System.err.println("更新学生状态失败，学生ID: " + sz.getStudent_id() + ", 错误: " + e.getMessage());
                }
            }

            // 每批之间稍作停顿，避免数据库压力过大
            if (i + batchSize < studentsToUpdate.size()) {
                try {
                    Thread.sleep(10); // 10ms停顿
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * 记录操作日志
     */
    private void logOperation(String content) {
        try {
            OperationLog operationLog = new OperationLog();
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setCreateTime(new Date());
            operationLog.setContent(content);
            operationLogService.insert(operationLog);
        } catch (Exception e) {
            System.err.println("记录操作日志失败: " + e.getMessage());
        }
    }
}
