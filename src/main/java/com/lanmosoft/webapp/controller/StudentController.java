/**
 * auto generated
 * Copyright (C) 2013 lanmosoft.com, All rights reserved.1355
 */
package com.lanmosoft.webapp.controller;

import com.lanmosoft.dao.model.*;
import com.lanmosoft.enums.Enums;
import com.lanmosoft.enums.Enums.*;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.*;
import com.lanmosoft.util.*;
import com.lanmosoft.web.StudentServiceImpl;
import com.lanmosoft.webapp.controller.searchForm.Page;
import com.lanmosoft.webapp.webmodel.LoginModel;
import com.lanmosoft.webapp.webmodel.StudentKeshi;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

/**
 * <p>
 * auto genegrated
 * </p>
 * <p>
 * Author: lanmo
 * </p>
 */
@Controller
public class StudentController extends BaseController {

    @Autowired
    StudentService studentService;
    @Autowired
    CityService cityService;
    @Autowired
    ZoneService zoneService;
    @Autowired
    StudentCodeUtil studentCodeUtil;
    @Autowired
    ContractService contractService;
    @Autowired
    ClassScheduleService classScheduleService;
    @Autowired
    TongjiService tongjiService;
    @Autowired
    Student_refundFeeService student_refundFeeService;
    @Autowired
    StudentServiceImpl studentServiceImpl;
    @Autowired
    Student_courseService student_courseService;
    @Autowired
    TutorService tutorService;
    @Autowired
    Consumption_studentService consumption_studentService;
    @Autowired
    Consumption_student_contractService consumption_student_contractService;
    @Autowired
    View_attendanceBookService view_attendanceBookService;
    @Autowired
    View_consumption_student_contractService view_consumption_student_contractService;
    @Autowired
    Student_zoneService student_zoneService;
    @Autowired
    ContractviewService contractviewService;

    @RequestMapping(value = "/ngres/xueshengguanli/student/findKeshi")
    public String executezz(ModelMap model, @RequestBody String content,
                            String age, HttpServletRequest request, HttpServletResponse response) {
        try {
	/*          d.courseType=='1'&&'正常课程'||
				d.courseType=='2'&&'少发需'||   不上课，发老师工资，扣学员课时
				d.courseType=='3'&&'多发（上课，不发工资，不扣学时）'||
				d.courseType=='4'&&'多发(上课，发工资，不扣学时)'}}*/
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject job = jsonObj.getJSONObject("item");
            String BUType = JSONUtils.getStr(job, "BUType");
            String zoneID = JSONUtils.getStr(job, "zone_id");
            Student p = new Student();
            JSONObject.toBean(job, p, jsonConfig);
            WhereCondition wc = new WhereCondition();
            wc.andEquals("studentId", p.getId());
            wc.andNotEquals("delStatus", "1");

            Double sum1 = 0d;//总课时 = 合同的amount之和X2
            Double sum2 = 0d;//已消耗 = 合同的consumedclass + consumption_student_contract中contract_id为null的consumedclass之和
            Double sumSPE = 0d; //specialist total hours
            Double sumOBT = 0d;

            List<Contract> query = contractService.query(wc);
            for (Contract contract : query) {
                sum1 += contract.getAmount() * 2;
                sum2 += contract.getConsumedClass() == null ? 0 : contract.getConsumedClass();
            }
            //specialist total hours
            wc.andEquals("contractType2", ContractType2.SPE);
            List<Contract> querySPE = contractService.query(wc);
            for (Contract contract : querySPE) {
                sumSPE += contract.getAmount() * 2;
            }

            //obt total hours
            wc.remove("contractType2");
            wc.andEquals("contractType2", ContractType2.OBT);
            List<Contract> queryOBT = contractService.query(wc);
            for (Contract contract : queryOBT) {
                sumOBT += contract.getAmount() * 2;
            }

            wc = new WhereCondition();
            wc.andEquals("student_id", p.getId());
            wc.andIsNull("contract_id");
            List<View_consumption_student_contract> listYipaike2 = view_consumption_student_contractService.queryCSC(wc);
            if (!listYipaike2.isEmpty()) {
                for (View_consumption_student_contract v : listYipaike2) {
                    sum2 += v.getConsumedClass() == null ? 0 : v.getConsumedClass();
                }
            }

            //未签到排课 = classchedule中status=0   +   classchedule.status=1 and attendancebook中id为null
            Double sumWeiqd = 0d;
            wc = new WhereCondition();
            wc.andEquals("student_id", p.getId());
            wc.andEquals("status", 0);
            List<View_attendanceBook> listWeiQd1 = view_attendanceBookService.query(wc);
            wc.remove("status");
            wc.andEquals("status", 1);
            wc.andIsNull("attendanceBook_id");
            List<View_attendanceBook> listWeiQd2 = view_attendanceBookService.query(wc);
            listWeiQd1.addAll(listWeiQd2);
            if (!listWeiQd1.isEmpty()) {
                for (View_attendanceBook va : listWeiQd1) {
                    sumWeiqd += va.getTime() == null ? 0 : Double.parseDouble(va.getTime());
                }
            }
            //已排课 =  已消耗+未签到的排课
            Double sum3 = 0d;
            sum3 = MathUtils.add(sum2, sumWeiqd);
            //未排课= 总课时-已排课
            Double sum4 = 0d;
            sum4 = MathUtils.sub(sum1, sum3);


            //Offline已耗 = offline contract.consumedclass + classschedule中BUtype为1的课对应的consumption_student_contract表中hybrid 合同或contract_id为null的耗课小时数
            Double sumOfflineYh = 0d;
            wc = new WhereCondition();
            //计算offline contract.consumedclass
            wc.andEquals("studentId", p.getId());
            wc.andEquals("contractType2", 1);
            List<Contract> listOffYh1 = contractService.query(wc);
            if (!listOffYh1.isEmpty()) {
                for (Contract c : listOffYh1) {
                    sumOfflineYh += c.getConsumedClass() == null ? 0 : c.getConsumedClass();
                }
            }
            //计算classschedule中BUtype为1的课对应的consumption_student_contract表中hybrid 合同或contract_id为null的耗课小时数
            wc = new WhereCondition();
            wc.andEquals("student_id", p.getId());
            wc.andEquals("BUType", "1");
            wc.andIsNull("contract_id");
            List<View_consumption_student_contract> listOffYh2 = view_consumption_student_contractService.queryCSC(wc);
            wc.remove("contract_id");
            wc.andIsNotNull("contract_id");
            wc.andEquals("contractType2", "3");
            List<View_consumption_student_contract> listoffYh3 = view_consumption_student_contractService.queryCSC(wc);
            listOffYh2.addAll(listoffYh3);
            if (!listOffYh2.isEmpty()) {
                for (View_consumption_student_contract v : listOffYh2) {
                    sumOfflineYh += v.getConsumedClass() == null ? 0 : v.getConsumedClass();
                }
            }
            //Online已耗 = classschedule表中BUtype为2的课对应的consumption_student.consumedclass
            Double sumOnlineYh = 0d;
            wc = new WhereCondition();
            wc.andEquals("student_id", p.getId());
            wc.andEquals("BUType", "2");
            List<View_consumption_student_contract> listOnYh = view_consumption_student_contractService.queryCS(wc);
            if (!listOnYh.isEmpty()) {
                for (View_consumption_student_contract v : listOnYh) {
                    sumOnlineYh += v.getConsumedClass_ct() == null ? 0 : v.getConsumedClass_ct();
                }
            }

            //Spe已耗 = classschedule表中BUtype为4的课对应的consumption_student.consumedclass
            Double sumSpeYh = 0d;
            wc = new WhereCondition();
            wc.andEquals("student_id", p.getId());
            wc.andEquals("BUType", "3");
            List<View_consumption_student_contract> listSpeYh = view_consumption_student_contractService.queryCS(wc);
            if (!listSpeYh.isEmpty()) {
                for (View_consumption_student_contract v : listSpeYh) {
                    sumSpeYh += v.getConsumedClass_ct() == null ? 0 : v.getConsumedClass_ct();
                }
            }

            //OBT已耗 = classschedule表中BUtype为4的课对应的consumption_student.consumedclass
            Double sumOBTYh = 0d;
            wc = new WhereCondition();
            wc.andEquals("student_id", p.getId());
            wc.andEquals("BUType", "4");
            List<View_consumption_student_contract> listOBTYh = view_consumption_student_contractService.queryCS(wc);
            if (!listOBTYh.isEmpty()) {
                for (View_consumption_student_contract v : listOBTYh) {
                    sumOBTYh += v.getConsumedClass_ct() == null ? 0 : v.getConsumedClass_ct();
                }
            }

            //offline已排未耗 online已排未耗
            Double sumoffYipwh = 0d;
            Double sumonYipwh = 0d;
            Double sumSpeYipwh = 0d;
            Double sumOBTYipwh = 0d;
            if (!listWeiQd1.isEmpty()) {
                for (View_attendanceBook va : listWeiQd1) {
                    if ("1".equals(va.getBUType())) {
                        sumoffYipwh += va.getTime() == null ? 0 : Double.parseDouble(va.getTime());
                    }
                    if ("2".equals(va.getBUType())) {
                        sumonYipwh += va.getTime() == null ? 0 : Double.parseDouble(va.getTime());
                    }
                    if ("3".equals(va.getBUType())) {
                        sumSpeYipwh += va.getTime() == null ? 0 : Double.parseDouble(va.getTime());
                    }
                    if ("4".equals(va.getBUType())) {
                        sumOBTYipwh += va.getTime() == null ? 0 : Double.parseDouble(va.getTime());
                    }
                }
            }
            //Offline已排 = offline已耗+offline已排未耗
            Double sumOfflineYp = MathUtils.add(sumOfflineYh, sumoffYipwh);
            //Online已排 = online已耗+online已排未耗
            Double sumOnlineYp = MathUtils.add(sumOnlineYh, sumonYipwh);
            //Spe已排 = Spe已耗+Spe已排未耗
            Double sumSpeYp = MathUtils.add(sumSpeYh, sumSpeYipwh);
            Double sumOBTYp = MathUtils.add(sumOBTYh, sumOBTYipwh);
            StudentKeshi st = new StudentKeshi();
            st.setSumKeshi(sum1);
            st.setYixiaohao(sum2);
            st.setYipaike(sum3);
            st.setWeipaike(sum4);
            st.setOfflineYihao(sumOfflineYh);
            st.setOfflineYipai(sumOfflineYp);
            st.setOnlineYihao(sumOnlineYh);
            st.setOnlineYipai(sumOnlineYp);

            st.setSpeYihao(sumSpeYh);
            st.setObtYihao(sumOBTYh);
            st.setSpeYipai(sumSpeYp);
            st.setObtYipai(sumOBTYp);

            st.setSpeTotal(sumSPE);

            st.setObtTotal(sumOBT);

            Map map = new HashMap();
            map.put("item", st);
            String string = JSONObject.fromObject(map).toString();
            AjaxUtils.ajaxJson(response, string);
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }


    @RequestMapping(value = "/ngres/xueshengguanli/student/edit")
    public String execute(ModelMap model, @RequestBody final String content,
                          String age, HttpServletRequest request, final HttpServletResponse response) {
        final String uid = getLogin(request).getUser().getId();
        final String uname = getLogin(request).getUser().getUserName();
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                try {
                    JSONObject jsonObj = JSONObject.fromObject(content);
                    JSONObject job = jsonObj.getJSONObject("item");

                    Student p = new Student();
                    JSONObject.toBean(job, p, jsonConfig);

                    if (StringUtils.isNotEmpty(p.getId())) {
                        Student old = studentService.loadById(p.getId());
                        Date nowDate = LanDateUtils.getNowDate();
                        //是否已经有code
                        if (StringUtils.isEmpty(p.getCode())) {
                            if (p.getCity_id() != null && p.getZone_id() != null) {
                                p.setCode(sequenceManager.generateNO("student", "", "", false));
                            }
                        }
                        if (StringUtils.isEmpty(old.getTutorId())) {
                            if (StringUtils.isNotEmpty(p.getTutorId())) {
                                Tutor tutor = new Tutor();
                                tutor.setId(sequenceManager.generateId("tutor"));
                                tutor.setStudent_id(p.getId());
                                tutor.setTutor_id(p.getTutorId());
                                tutor.setStartDate(LanDateUtils.getNowDate());
                                tutorService.insert(tutor);
                                String context = uname + "为学生：" + p.getId() + "新增助教" + tutor.getId();
                                operationLog.setId(sequenceManager.generateId("operationLog"));
                                operationLog.setUser_id(uid);
                                operationLog.setCreateTime(new Date());
                                operationLog.setContent(context);
                                operationLogService.insert(operationLog);
                            }
                        } else {
                            if (!StringUtils.equals(p.getTutorId(), old.getTutorId())) {
                                WhereCondition wc = new WhereCondition();
                                wc.andEquals("tutor_id", old.getTutorId());
                                wc.andEquals("student_id", old.getId());
                                wc.andIsNull("endDate");
                                Tutor tutor = new Tutor();
                                tutor.setEndDate(nowDate);
                                tutorService.updateByCondition(wc, tutor);
                                tutor = new Tutor();
                                tutor.setId(sequenceManager.generateId("tutor"));
                                tutor.setStudent_id(p.getId());
                                tutor.setTutor_id(p.getTutorId());
                                tutor.setStartDate(nowDate);
                                tutorService.insert(tutor);
                                String context = uname + "为学生：" + p.getId() + "新增助教" + tutor.getId() + ",结束老的助教" + old.getTutorId();
                                operationLog.setId(sequenceManager.generateId("operationLog"));
                                operationLog.setUser_id(uid);
                                operationLog.setCreateTime(new Date());
                                operationLog.setContent(context);
                                operationLogService.insert(operationLog);
                            }

                        }

                        //冻结，标记删除排课信息
                        if (StringUtils.equals(Enums.TRUE_STRING, JSONUtils.getStr(job, "isThawed"))) {
                            p.setStatus(StudentStatus.STAUS_5);
                            p.setFreezeDate(nowDate);

                            //只标记删除未请假的课程，请假的课程不删除[明日，解冻日期)
                            WhereCondition wc = new WhereCondition();
                            wc.andEquals("student_id", p.getId());
                            //Date tomorrow =LanDateUtils.getNext_Day(LanDateUtils.getNowDate(), 1);
                            Date tomorrow = p.getThawedDateBegin();
                            wc.andGreaterEquals("scheduledDate", LanDateUtils.format(tomorrow, "yyyy-MM-dd"));
                            wc.andLessThan("scheduledDate", LanDateUtils.format(LanDateUtils.getNext_Day(p.getThawedDate(), -1), "yyyy-MM-dd"));
                            wc.andEquals("leaveState", LeaveState.LEAVE_NONE);
                            ClassSchedule t = new ClassSchedule();
                            t.setStatus(ClassScheduleStatus.DELETED);
                            classScheduleService.updateByCondition(wc, t);
                            String context = uname + "冻结学生" + p.getId() + "并删除排课信息，时间范围：" + tomorrow + "--" + LanDateUtils.getNext_Day(p.getThawedDate(), -1);
                            operationLog.setId(sequenceManager.generateId("operationLog"));
                            operationLog.setUser_id(uid);
                            operationLog.setCreateTime(new Date());
                            operationLog.setContent(context);
                            operationLogService.insert(operationLog);
                        } else {
                            if (StringUtils.equals(StudentStatus.STAUS_5, p.getStatus())) {
                                p.setStatus("");
                                p.setFreezeDate(null);
                                p.setThawedDate(null);
                            }
                        }
                        studentService.updateForce(p);
                        String context = uname + "修改了学生信息";
                        operationLog.setId(sequenceManager.generateId("operationLog"));
                        operationLog.setUser_id(uid);
                        operationLog.setCreateTime(new Date());
                        operationLog.setContent(context);
                        operationLogService.insert(operationLog);
                    } else {
                        String id = sequenceManager.generateId("student");
                        p.setId(id);

                        studentService.insert(p);
                    }
                    AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
                } catch (Exception e) {
                    AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
                    e.printStackTrace();
                }
            }
        });

        return null;
    }

    @RequestMapping(value = "/ngres/xueshengguanli/student/delete")
    public String execute3(ModelMap model, @RequestBody String content,
                           String age, HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONArray jsonArray = JSONArray.fromObject(content);
            List<Student> list = new ArrayList<Student>();
            for (int i = 0; i < jsonArray.size(); i++) {
                Student p = new Student();
                JSONObject.toBean(JSONObject.fromObject(jsonArray.get(i)), p, jsonConfig);
                list.add(p);
            }
            List<String> ids = new ArrayList<String>();
            for (Student p : list) {
                ids.add(p.getId());
            }
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            studentService.deleteByCondition(wc);
            String context = getLogin(request).getUser().getUserName() + "删除了学生信息：" + ids;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0003");
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0004");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/xueshengguanli/student/list")
    public String execute1(ModelMap model, @RequestBody String content,
                           HttpServletRequest request, HttpServletResponse response) {
        try {
            System.out.println("content----------" + content);
            JSONObject jsonObj = JSONObject.fromObject(content);

            JSONObject searchItems = jsonObj.getJSONObject("searchItems");
            String totorId_eq = JSONUtils.getStr(searchItems, "tutorId_eq");
            String status_eq = JSONUtils.getStr(searchItems, "status_eq");
            String chineseName_lk = JSONUtils.getStr(searchItems, "chineseName_lk");
            String englishName_lk = JSONUtils.getStr(searchItems, "englishName_lk");
            String suoshu = JSONUtils.getStr(jsonObj, "suoshu");


            // 分页对象
            Page page = getPage(jsonObj);
            // 服务端排序规则
            String orderGuize = getOrderGuize(JSONUtils.getStr(jsonObj, "orderGuize"));
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            wc.setLength(page.getItemsperpage());
            wc.setOffset((page.getCurrentPage() - 1) * page.getItemsperpage());
            wc.setOrderBy(orderGuize);
            initWanNengChaXun(jsonObj, wc);// 万能查询
            String zone_id = JSONUtils.getStr(jsonObj, "zone_id");
            Boolean tutorFlag = false;
            String tutorId = "";
            if (!"0".equals(getLogin(request).getUser().getCategory().trim())) {
                List<Permission> li = getLogin(request).getPermissionList();
                for (Permission pm : li) {
                    if ("133".equals(pm.getFunction_id())) {
                        tutorFlag = true;
                        tutorId = getLogin(request).getUser().getId();

                    }
                }
            }


            List<Student> list = null;
            int count = 0;
            //判断是否是学员上课报表(只对学员上课报表)
            if (StringUtils.equalsIgnoreCase("suoshu", suoshu)) {

                //获取该用户所属权限对应的校区
                String zoneWhere = " ";
                //数据级权限受当前登录用户的权限限制。如果有 所有校区 权限，则课显示全部校区课表；否则显示用户有权限的校区的课
                List<String> listZoneId = ZoneUtils.findZoneIdByUser(request, "");
                String zondids = ZoneUtils.changeString(listZoneId);
                if (StringUtils.isNotBlank(zondids)) {
                    zoneWhere += " AND ( a.zone_id IN ( " + zondids + " ) ";
                    zoneWhere += " 	OR a.id IN ( ";
                    zoneWhere += " 	SELECT DISTINCT s.id FROM student_zone sz LEFT JOIN student s ON s.id = sz.student_id ";
                    zoneWhere += " 	WHERE sz.zone_id IN ( " + zondids + " )AND s.zone_id != sz.zone_id ";
                    zoneWhere += " )) ";
                }


                String select = " SELECT * FROM student a ";
                String selectCount = " SELECT count(1) FROM student a ";
                String where = " WHERE 1=1 " + zoneWhere;
                if (StringUtils.isNotBlank(chineseName_lk)) {
                    where += " and a.chineseName LIKE '%" + chineseName_lk + "%' ";
                }
                if (StringUtils.isNotBlank(englishName_lk)) {
                    where += " and a.englishName LIKE '%" + englishName_lk + "%' ";
                }
                if (tutorFlag) {
                    select = " SELECT DISTINCT a.* FROM student a LEFT JOIN student_zone b on a.id = b.student_id ";
                    selectCount = " SELECT count(DISTINCT a.id) FROM student a LEFT JOIN student_zone b on a.id = b.student_id ";
                    where += " and b.tutorId ='" + tutorId + "' ";
                }
                String limt = " limit " + (page.getCurrentPage() - 1) * page.getItemsperpage() + "," + page.getItemsperpage();
                Zidingyi zi = new Zidingyi();
                zi.setSql(select + where + limt);
                List<Map> query = tongjiService.query(zi);
                if (CollectionUtils.isNotEmpty(query)) {
                    List<Student> list1 = new ArrayList<Student>();
                    for (Map m : query) {
                        Student s = new Student();
                        BeanTranUtil.transMap2Bean1(m, s);
                        list1.add(s);
                    }
                    list = list1;
                    //montageData(list);
                }
                zi.setSql(selectCount + where);
                count = tongjiService.count(zi);
            } else {
                //判断校区,如果是全部校区,和单个校区
                if ("all".equals(zone_id) && StringUtils.isBlank(totorId_eq) && StringUtils.isBlank(status_eq)) {
                    list = studentService.queryStudent(wc);
                    //拼接剩余数据,上课状态,城市,校区,助教,OC课程状态
                    montageData(list);
                    count = studentService.countStudent(wc);
                } else if ("allstudents".equals(zone_id) && StringUtils.isBlank(totorId_eq) && StringUtils.isBlank(status_eq)) {
                    // get zone list of normal user
                    List<String> listZoneId = ZoneUtils.findZoneIdByUser(request, "");
                    zonePermissionFilter(jsonObj, wc, request, response, listZoneId);// 校区权限过滤
                    if (tutorFlag) {
                        wc.andEquals("tutorId", tutorId);
                    }
                    list = studentService.query(wc);
                    count = studentService.count(wc);
                } else {
                    zonePermissionFilter(jsonObj, wc, request, response, zone_id);// 校区权限过滤
                    if (tutorFlag) {
                        wc.andEquals("tutorId", tutorId);
                    }
                    if (searchItems.get("Type_eq") != null && searchItems.get("Type_eq").equals("9")) {
                        list = studentService.queryStudent(wc);
                    } else {
                        list = studentService.query(wc);
                    }

                    count = studentService.count(wc);
                }
            }

            // get student total hours and consumed class
            String ids = "";
            for (Student s : list) {
                ids += "'" + s.getId() + "',";
            }
            Zidingyi zi = new Zidingyi();
            String sql = "SELECT student.id, student.accountId, student.ownerName AS Consultant, student.englishName AS Student, student.chineseName AS ChineseName, a.TotalHours, a.ConsumedClass, a.Balance, b.TotalSchedule, ( a.TotalHours - b.TotalSchedule ) AS ToBeScheduled, student.classscheduleStatus, ( CASE student.classscheduleStatus WHEN 0 THEN 'Active' WHEN 1 THEN 'Inactive' WHEN 3 THEN 'Closed' ELSE 'Half-Closed' END ) AS STATUS, CONCAT( cast((( a.ConsumedClass / a.TotalHours )* 100 ) AS DECIMAL ( 18, 2 )), '%' ) AS ConsumptionRate  FROM student LEFT JOIN Zone ON zone.id = student.zone_id LEFT JOIN ( SELECT contract.studentId, sum( contract.amount * 2 ) AS TotalHours, sum( contract.consumedClass ) AS ConsumedClass, ( sum( contract.amount * 2 )- sum( contract.consumedClass ) - ifnull( BB.ConsumedClassNoContract, 0 )) AS Balance  FROM contract LEFT JOIN ( SELECT student_id, sum( consumedClass ) AS ConsumedClassNoContract FROM view_consumption_student_contract WHERE contract_id IS NULL GROUP BY student_id ) AS BB ON BB.student_id = contract.studentId  WHERE delStatus <> '1'  GROUP BY contract.studentId  ) AS a ON a.studentId = student.id LEFT JOIN ( SELECT classschedule.student_id AS studentId, sum( classschedule.time ) AS TotalSchedule FROM `classschedule` WHERE classschedule.`status` <> '2' GROUP BY classschedule.student_id ) AS b ON b.studentId = student.id ";
            String where = " where student.id in(" + ids + "'')";
            zi.setSql(sql + where);
            List<Map> query = tongjiService.query(zi);
            if (CollectionUtils.isNotEmpty(query)) {
                for (Map m : query) {
                    for (Student s : list) {
                        if (s.getId().equals(m.get("id").toString())) {
                            s.setTotalHours(m.get("TotalHours") != null ? m.get("TotalHours").toString() : "");
                            s.setConsumedClass(m.get("ConsumedClass") != null ? m.get("ConsumedClass").toString() : "");
                        }
                    }
                }
            }

            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            page.setTotalItems(count);
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 拼接数据
     *
     * @param list
     */
    private void montageData(List<Student> list) {
        WhereCondition wcStudent = new WhereCondition();
        if (!list.isEmpty()) {
            for (Student student : list) {
                String id = student.getId();
                //查询多个校区对应的上课状态,城市,校区,助教,OC课程状态
                wcStudent.clear();
                wcStudent.andEquals("student_id", id);
                String status = "";
                String city_name = "";
                String zone_name = "";
                String tutor_name = "";
                String oc = "";
                List<Student_zone> listSz = student_zoneService.query(wcStudent);
                if (!listSz.isEmpty()) {
                    for (Student_zone sz : listSz) {
                        status += (sz.getClassStatus() == null ? "" : ";" + sz.getClassStatus());
                        city_name += (sz.getCity_name() == null ? "" : ";" + sz.getCity_name());
                        zone_name += (sz.getZone_name() == null ? "" : ";" + sz.getZone_name());
                        tutor_name += (sz.getTutor_name() == null ? "" : ";" + sz.getTutor_name());
                        oc += (sz.getIsOC() == null ? "" : ";" + sz.getIsOC());
                    }
                    status = 0 == status.indexOf(";") ? status.substring(1) : status;
                    city_name = 0 == city_name.indexOf(";") ? city_name.substring(1) : city_name;
                    zone_name = 0 == zone_name.indexOf(";") ? zone_name.substring(1) : zone_name;
                    tutor_name = 0 == tutor_name.indexOf(";") ? tutor_name.substring(1) : tutor_name;
                    oc = 0 == oc.indexOf(";") ? oc.substring(1) : oc;
                }

                student.setStatus(status);
                student.setCity_name(city_name);
                student.setZone_name(zone_name);
                student.setTutor_englishName(tutor_name);
                student.setIsOC(oc);
            }
        }
    }

    //OC转换
    private String changeOC(String isOC) {

        return "";
    }

    //状态转换
    private String changeStatus(String status) {

        if ("1".equals(status)) {
            return "欠费";
        }
        if ("2".equals(status)) {
            return "待确认";
        }
        if ("3".equals(status)) {
            return "执行中";
        }
        if ("4".equals(status)) {
            return "已结束";
        }
        if ("5".equals(status)) {
            return "冻结";
        }
        return "";
    }

    @RequestMapping(value = "/ngres/xueshengguanli/student/listCheck")
    public String executeCheck(ModelMap model, @RequestBody String content,
                               HttpServletRequest request, HttpServletResponse response) {
        try {
            System.out.println("content----------" + content);
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONArray sids = jsonObj.getJSONArray("sids");
            List<String> ids = new ArrayList<String>();
            for (int j = 0; j < sids.size(); j++) {
                ids.add(sids.getString(j));
            }
            // 组装查询条件
            WhereCondition wc = new WhereCondition();
            wc.andIn("id", ids);
            List list = studentService.query(wc); //
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);
            Map map = new HashMap();
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/xueshengguanli/student/list/id")
    public String executesd1(ModelMap model, @RequestBody String content,
                             HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            Student k = studentService.loadById(JSONUtils.getStr(jsonObj, "id"));
            JSONObject jo = JSONObject.fromObject(k, jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/xueshengguanli/studentReturns/list")
    public String executeReturnsList(ModelMap model, @RequestBody String content,
                                     HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            // 分页对象
            Page page = getPage(jsonObj);
            String sql = "select b.id as studentId,a.id as hetongId,d.id as id,a.contractNo as hetong,b.`code` as xuehao,b.chineseName as xingming,c.`name` as xiaoqu,d.periods as tuifeikeshi,d.refundFuitionFee as tuifeijine,d.handleTime as tuifeishijian,d.refundReason as tuifeiyuanyin,d.approvalStatus zhuangtai";
            String sqlAll = " SELECT b.id as studentId,a.id AS hetongId, d.id AS id, a.contractNo AS hetong, b.`code` AS xuehao, b.chineseName AS xingming, d.periods AS tuifeikeshi, d.refundFuitionFee AS tuifeijine, d.handleTime AS tuifeishijian, d.refundReason AS tuifeiyuanyin, d.approvalStatus zhuangtai ";
            String sql1 = "select count(*) ";
            String from = " FROM contract a LEFT JOIN student b ON a.studentId = b.id LEFT JOIN student_zone sz on sz.student_id = b.id LEFT JOIN zone c ON c.id = sz.zone_id LEFT JOIN student_refundFee d ON a.id = d.contract_id ";
            String fromAll = " FROM contract a LEFT JOIN student b ON a.studentId = b.id LEFT JOIN student_refundFee d ON a.id = d.contract_id ";
            String where = " where 1=1 AND d.approvalStatus in('1','2','3','4') ";
            JSONObject jsonObjsearchItems = jsonObj.getJSONObject("searchItems");
            Set set = jsonObjsearchItems.keySet();
            for (Object o : set) {
                String key = o.toString();
                String value = jsonObjsearchItems.getString(key);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                System.out.println(key + "---" + value);
                if ("hetong".equals(key.trim())) {
                    where += " and a.contractNo like '%" + value + "%'";
                }
                if ("xingming".equals(key.trim())) {
                    where += " and b.chineseName like '%" + value + "%'";
                }
                if ("studentId".equals(key.trim())) {
                    where += " and a.studentid='" + value + "'";
                }
            }
            String sqlSql = " ";
            String sqlCount = "";
            //追加   校区条件
            //String zone_id = getLogin(request).getZone_id();
            String zone_id = JSONUtils.getStr(jsonObj, "zone_id");
            if (StringUtils.isEmpty(zone_id)) {
                where += " and sz.zone_id='" + getLogin(request).getUser().getZone_id() + "'";
            } else {
                if (!StringUtils.equalsIgnoreCase("all", zone_id)) {
                    where += " and sz.zone_id='" + zone_id + "'";
                    if (!"0".equals(getLogin(request).getUser().getCategory().trim())) {
                        List<Permission> li = getLogin(request).getPermissionList();
                        for (Permission pm : li) {
                            if ("133".equals(pm.getFunction_id())) {
                                where += " and sz.tutorId='" + getLogin(request).getUser().getId() + "'";
                            }
                        }
                    }
                    sqlSql = sql + from + where;
                    sqlCount = sql1 + from + where;
                } else {
                    sqlSql = sqlAll + fromAll + where;
                    sqlCount = sql1 + fromAll + where;
                }
            }


            Zidingyi z = new Zidingyi();
            if (!sqlSql.contains("limit")) {
                sqlSql = sqlSql + " limit " + (page.getCurrentPage() - 1) * page.getItemsperpage() + "," + page.getCurrentPage() * page.getItemsperpage();
            }
            z.setSql(sqlSql);
            List<Map> li = tongjiService.query(z);
            List<StudentReturns> list = new ArrayList<StudentReturns>();
            Map<String, String> mapStuzone = new HashMap<String, String>();
            for (Map m : li) {
                StudentReturns sr = new StudentReturns();
                String studentId = m.get("studentId") != null ? m.get("studentId").toString() : "";

                sr.setHetong(m.get("hetong") != null ? m.get("hetong").toString() : "");
                sr.setId(m.get("id") != null ? m.get("id").toString() : "");
                sr.setTuifeijine(m.get("tuifeijine") != null ? m.get("tuifeijine").toString() : "");
                sr.setTuifeikeshi(m.get("tuifeikeshi") != null ? m.get("tuifeikeshi").toString() : "");
                sr.setTuifeishijian(m.get("tuifeishijian") != null ? m.get("tuifeishijian").toString() : "");
                sr.setTuifeiyuanyin(m.get("tuifeiyuanyin") != null ? m.get("tuifeiyuanyin").toString() : "");
                sr.setXiaoqu(m.get("xiaoqu") != null ? m.get("xiaoqu").toString() : "");
                sr.setXingming(m.get("xingming") != null ? m.get("xingming").toString() : "");
                sr.setXuehao(m.get("xuehao") != null ? m.get("xuehao").toString() : "");
                sr.setZhuangtai(m.get("zhuangtai") != null ? m.get("zhuangtai").toString() : "");
                sr.setHetongId(m.get("hetongId") != null ? m.get("hetongId").toString() : "");

                if ("all".equalsIgnoreCase(zone_id)) {
                    getStuZone(studentId, mapStuzone);

                    sr.setXiaoqu(mapStuzone.get("zone_name"));
                }

                list.add(sr);
            }
            z.setSql(sqlCount);
            int count = tongjiService.count(z);
            JSONArray ja = JSONArray.fromObject(list, jsonConfig);

            page.setTotalItems(count);
            Map map = new HashMap();
            map.put("page", page);
            map.put("list", ja);
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
        } catch (BadSqlGrammarException b) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, b.getCause().getMessage());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取studnet_zone拼接的校区,等等信息
     *
     * @param studentId
     * @param mapStuzone
     * @return
     */
    private void getStuZone(String studentId, Map<String, String> mapStuzone) {
        mapStuzone.clear();
        WhereCondition wc = new WhereCondition();
        wc.andEquals("student_id", studentId);
        List<Student_zone> list = student_zoneService.query(wc);

        if (!list.isEmpty()) {
            for (Student_zone sz : list) {
                String zonename = sz.getZone_name();
                String zone_name = mapStuzone.get("zone_name");
                if (StringUtils.isBlank(zone_name)) {
                    mapStuzone.put("zone_name", zonename);
                } else {
                    mapStuzone.put("zone_name", zone_name + "; " + zonename);
                }
            }
        }
    }


    @RequestMapping(value = "/ngres/xueshengguanli/studentReturns/list/tuifei")
    public String executeTuifei(ModelMap model, @RequestBody String content,
                                HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            String sql = "select b.chineseName as xingming,b.`code` as xuehao,a.amount as danyuan,a.tuitionFee as yifujine,a.consumedClass as yiyongkeshi,c.handleTime as tuifeishijian,"
                    + " c.refundType as tuifeileixing,c.refundReason as yuanyin,c.refundFuitionFee as tuifeijine,a.id as hetongId,(select p.productName from product p where p.id=c.new_prorduct_id) chanpin,c.unitPrice as danjia "
                    + " from contract a,student b,student_refundFee c where a.studentId=b.id and c.contract_id=a.id and a.id='" + JSONUtils.getStr(jsonObj, "id") + "'";
            Zidingyi z = new Zidingyi();
            z.setSql(sql);
            List<Map> li = tongjiService.query(z);
            StudentReturns sr = new StudentReturns();
            if (li != null && !li.isEmpty()) {
                Map m = li.get(0);
                double xh = Double.valueOf(m.get("yiyongkeshi") != null ? m.get("yiyongkeshi").toString() : "0");
                double zks = Double.valueOf(m.get("danyuan") != null ? m.get("danyuan").toString() : "0") * 2;
                double ks = zks - xh;
                sr.setXuehao(m.get("xuehao") != null ? m.get("xuehao").toString() : "");
                sr.setXingming(m.get("xingming") != null ? m.get("xingming").toString() : "");
                sr.setRiqi(new Date());
                sr.setDanyuanshu(m.get("danyuan") != null ? m.get("danyuan").toString() : "0");
                sr.setTuifeikeshi(ks + "");
                sr.setTuifeijine(m.get("tuifeijine") != null ? m.get("tuifeijine").toString() : "0");
                sr.setHetongId(m.get("hetongId") != null ? m.get("hetongId").toString() : "");
                sr.setAmount(m.get("danyuan") != null ? m.get("danyuan").toString() : "0");
                sr.setXiaohaokeshi(xh + "");
                sr.setYifukuanjine(m.get("yifujine") != null ? m.get("yifujine").toString() : "0");
                sr.setTuifeishijian(m.get("tuifeishijian") != null ? m.get("tuifeishijian").toString() : "");
                sr.setChanpin(m.get("chanpin") != null ? m.get("chanpin").toString() : "");
                sr.setDanjia(m.get("danjia") != null ? m.get("danjia").toString() : "");
                sr.setType(m.get("tuifeileixing") != null ? m.get("tuifeileixing").toString() : "");
                sr.setTuifeiyuanyin(m.get("yuanyin") != null ? m.get("yuanyin").toString() : "");
            }
            JSONObject jo = JSONObject.fromObject(sr, jsonConfig);
            AjaxUtils.ajaxJson(response, jo.toString());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }


    @RequestMapping(value = "/ngres/xueshengguanli/studentReturns/shenpi")
    public String executeReturnsShenpi(ModelMap model, @RequestBody String content,
                                       HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            Student_refundFee k = student_refundFeeService.loadById(JSONUtils.getStr(jsonObj, "id"));
            boolean flag = jsonObj.getBoolean("flag");
            if (flag) {
                if ("4".equals(k.getApprovalStatus())) {
                    k.setApprovalStatus("2");
                } else {
                    if (k.getRefundFuitionFee() > 150000) {
                        k.setApprovalStatus("4");
                    } else {
                        k.setApprovalStatus("2");
                    }
                }
            } else {
                k.setApprovalStatus("3");
            }
            k.setApproverTime(new Date());
            k.setApprover(getLogin(request).getUser().getUserName());
            student_refundFeeService.update(k);
            String context = getLogin(request).getUser().getUserName() + "审批了退费信息：" + k.getId();
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0020");
        } catch (BadSqlGrammarException b) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, b.getCause().getMessage());
        } catch (Exception e) {// TODO
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/xueshengguanli/student/sync")
    public String executesync(ModelMap model, @RequestBody String content,
                              String age, HttpServletRequest request, HttpServletResponse response) {
        try {

            JSONObject jsonObj = JSONObject.fromObject(content);
            //JSONObject job = jsonObj.getJSONObject("item");

            Student p = new Student();
            JSONObject.toBean(jsonObj, p, jsonConfig);

            String context = p.getEnglishName();
            //context += "同步老师信息到268" + p.getId();
            context += "创建tencentID" + p.getId();
            String displayname = p.getEnglishName();
            // 如果p.getMobile()中的内容以“-”分割，那么前面为区号，放入area变量，后面为手机号，放入phone变量
            String area = "86";
            String phone = "";
            if (p.getMobile().contains("-")) {
                String[] arr = p.getMobile().split("-");
                area = arr[0];
                phone = arr[1];
            } else {
                phone = p.getMobile();
            }

            //String phone = p.getMobile();
            String username = p.getAccountId().substring(p.getAccountId().length() - 8);
            String email = username + "@be.co";
            String type = "s";
            String deptid = "dp-9e563eaa95ea48e3937bf281e3183666";

            try {
                String res = TencentUtil.createTencentAccount(username, email, displayname, phone, deptid, type, area);
                if (StringUtils.contains(res, "error") || StringUtils.contains(res, "account duplicated") || StringUtils.equals(res, "")) {
                    context += "创建腾讯账户错误" + p.getId();
                } else {
                    context += "创建腾讯账户成功" + p.getId();
                    WhereCondition wc = new WhereCondition();
                    wc.andEquals("id", p.getId());
                    Student st = new Student();
                    st.setTencentid(res);
                    studentService.updateByCondition(wc, st);

                }
            } catch (Exception e) {
                e.printStackTrace();
            }


            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);

            String s = context;
            AjaxUtils.ajaxJson(response, s);

        } catch (Exception e) {// TODO
            Map map = new HashMap();
            map.put("status", "success");
            map.put("message", "new_toastr_0001");
            String s = JSONObject.fromObject(map, jsonConfig).toString();
            AjaxUtils.ajaxJson(response, s);
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/xueshengguanli/student/import")
    public String executeImport(ModelMap model, @RequestBody final String content,
                                final HttpServletRequest request, final HttpServletResponse response, String id) {
        FileInputStream fileStream = null;
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            String uniqueIdentifier = JSONUtils.getStr(jsonObj.getJSONArray("files").getJSONObject(0), "uniqueIdentifier");
            final String errorhanding = JSONUtils.getStr(jsonObj.getJSONObject("item"), "errorhanding");
            WhereCondition wc = new WhereCondition();
            wc.andEquals("reference_type", "shujudaoru");
            wc.setOrderBy("createTime desc");
            Filerecord fr = filerecordService.query(wc).get(0);
            String fullpath = fr.getLogicPath();
            System.out.println(fullpath);
            File f = new File(fullpath);
            fileStream = new FileInputStream(f);
            Workbook work = null;
            if (fullpath.toLowerCase().endsWith(".xls")) {
                work = new HSSFWorkbook(fileStream);
            } else {
                work = new XSSFWorkbook(fileStream);
            }
            final Sheet sheet = work.getSheetAt(0);
            final List<String> headList = new ArrayList<String>();
            //读取列信息
            Row row = sheet.getRow(1);
            int cellSumNum = row.getLastCellNum();
            for (int i = 0; i < cellSumNum; i++) {
                String biaoshiming = ExcelUtil.getRowCellStr(row, i);
                if (StringUtils.isEmpty(biaoshiming)) {
                    break;
                }
                headList.add(biaoshiming);
            }
            String context = getLogin(request).getUser().getUserName() + "导入了学生及合同数据：";
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                    User user = getLogin(request).getUser();
                    //插入数据
                    List<Map> list = new ArrayList<Map>();
                    int rowNum = sheet.getLastRowNum();
                    for (int i = 2; i <= rowNum; i++) {
                        try {
                            Row dataRow = sheet.getRow(i);
                            if (dataRow == null)
                                continue;
                            Map map = new HashMap();
                            for (int j = 0; j < headList.size(); j++) {
                                String value = ExcelUtil.getRowCellStr(dataRow, j);
                                map.put(headList.get(j).trim(), value);
                                System.out.println(i + ">  key-->" + headList.get(j) + "  value-->" + value);
                            }
                            if (map.get("contractId") != null && !"".equals(map.get("contractId").toString().trim())
                                    && !StringUtils.equalsIgnoreCase("null", map.get("contractId").toString())) {
                                list.add(map);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            if (StringUtils.equals(Enums.FALSE_STRING, errorhanding)) {
                                break;
                            } else {
                                continue;
                            }
                        }
                    }
                    Map m1 = new HashMap();
                    m1.put("Token", "nVu7MmVmvQ96yfvhFNR8fNke0Odo0Ypzw4wmcATCMRw=");
                    m1.put("ContractInfo", list);
                    String rt = studentServiceImpl.saveContract(JSONObject.fromObject(m1, jsonConfig).toString());
                    System.out.println("RT==>" + rt);
                }
            });
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0021");
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0022");
            e.printStackTrace();
        } finally {
            if (fileStream != null)
                try {
                    fileStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
        }
        return null;
    }

    @RequestMapping(value = "/ngres/xueshengguanli/student/createContract")
    public String createContract(ModelMap model, @RequestBody String content,
                                 HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            JSONObject job = jsonObj.getJSONObject("item");
            String reference_id = JSONUtils.getStr(job, "reference_id");
            String student_id = JSONUtils.getStr(job, "student_id");
            String contractNo = JSONUtils.getStr(job, "contractNo");
            double givenClass = job.getDouble("givenClass");
            //查询可用合同
            List<Contract> useableContracts = contractService.getUseableContract(reference_id, false);
            //创建合同
            Contract contract = new Contract();
            contract.setId(sequenceManager.generateId("contract"));
            // 合同开始日期
            if (CollectionUtils.isEmpty(useableContracts)) {
                contract.setStartDate(LanDateUtils.convertStringToDate("yyyy-MM-dd", "9999-12-31"));
            } else {
                contract.setStartDate(LanDateUtils.getNextDate_month(
                        useableContracts.get(useableContracts.size() - 1).getStartDate(), 1));
            }
            //合同结束日期
            contract.setEndDate(null);
            // 赠送类型
            WhereCondition wc = new WhereCondition();
            wc.andEquals("contractNo", contractNo);
            List<Contractview> list = contractviewService.query(wc);
            String contractType2 = "";
            if (!list.isEmpty()) {
                contractType2 = list.get(0).getContractType2();
            }
            contract.setContractType2(contractType2);
            contract.setPersentType(PersentType.TE_REFERRAL);
            contract.setStudentId(reference_id);// 学员
            contract.setUnits(givenClass / 2);// 单元数
            contract.setAmount(contract.getUnits());// 数量
            contract.setTuitionFee(0D);// 学费
            contract.setProductType(ProductType.ONE_TO_ONE); // 产品类型
            contract.setConsumedClass(0D);// 已消耗课时数
            contract.setProductName(ProductName.TE_REFERRAL);//产品名称
            contract.setContractNoR(contractNo);//关联合同号
            initCreate2(contract, request);
            contractService.insert(contract);
            //更新被介绍学员
            Student student = new Student();
            student.setId(student_id);
            student.setIsGiven(Enums.TRUE_STRING);
            studentService.update(student);
            String context = getLogin(request).getUser().getUserName() + "为学员" + reference_id + "创建推荐赠送合同，被介绍学员" + student_id;
            operationLog.setId(sequenceManager.generateId("operationLog"));
            operationLog.setUser_id(getLogin(request).getUser().getId());
            operationLog.setCreateTime(new Date());
            operationLog.setContent(context);
            operationLogService.insert(operationLog);
            AjaxUtils.ajaxJsonSuccessMessage(response, "new_toastr_0001");
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0002");
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/ngres/xueshengguanli/student/list/consumption")
    public String getConsumption(ModelMap model, @RequestBody String content,
                                 HttpServletRequest request, HttpServletResponse response) {
        try {
            JSONObject jsonObj = JSONObject.fromObject(content);
            String student_id = JSONUtils.getStr(jsonObj, "student_id");
            WhereCondition wc = new WhereCondition();
            wc.andEquals("student_id", student_id);
            List<Consumption_course> list = student_courseService.sumCourse(wc);
            Consumption_course sumCourse = new Consumption_course();
            sumCourse.setStudent_id(student_id);
            double totalClass = 0;
            double consumedClass = 0;
            System.out.println("-------" + list.size());
            for (Consumption_course course : list) {
                wc = new WhereCondition();
                wc.andEquals("student_id", student_id);
                wc.andEquals("course_id", course.getCourse_id());
                wc.andNotEquals("courseType", CourseType.COURSETYPE_2);
                wc.andNotEquals("status", 2);
                course.setConsumedClass(classScheduleService.sumClass(wc));
                if (course.getTotalClass() != null) {
                    totalClass += course.getTotalClass();
                } else {
                    totalClass += 0;
                }
                if (course.getConsumedClass() != null) {
                    consumedClass += course.getConsumedClass();
                } else {
                    consumedClass += 0;
                }


            }
            sumCourse.setTotalClass(totalClass);
            sumCourse.setConsumedClass(consumedClass);
            model.put("item", sumCourse);
            model.put("list", list);
            AjaxUtils.ajaxJson(response, JSONObject.fromObject(model, jsonConfig).toString());
        } catch (Exception e) {
            AjaxUtils.ajaxJsonErrorMessage(response, "new_toastr_0005");
            e.printStackTrace();
        }
        return null;
    }
}
