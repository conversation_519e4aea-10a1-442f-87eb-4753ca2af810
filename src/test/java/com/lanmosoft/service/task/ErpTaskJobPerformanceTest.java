package com.lanmosoft.service.task;

import com.lanmosoft.dao.model.Student_zone;
import com.lanmosoft.model.WhereCondition;
import com.lanmosoft.service.biz.Student_zoneService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * ErpTaskJob性能测试
 * 用于验证job4方法的优化效果
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext-resources.xml"})
public class ErpTaskJobPerformanceTest {

    @Autowired
    private ErpTaskJob erpTaskJob;
    
    @Autowired
    private Student_zoneService student_zoneService;

    /**
     * 测试原始版本的性能
     */
    @Test
    public void testOriginalJob4Performance() {
        System.out.println("=== 测试原始版本性能 ===");
        
        // 获取测试数据量
        WhereCondition wc = new WhereCondition();
        wc.andNotEquals("classStatus","5");
        List<Student_zone> students = student_zoneService.query(wc);
        System.out.println("测试数据量: " + students.size() + " 个学生");
        
        long startTime = System.currentTimeMillis();
        
        try {
            erpTaskJob.job4_original();
        } catch (Exception e) {
            System.err.println("原始版本执行失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("原始版本执行时间: " + duration + "ms");
        System.out.println("平均每个学生处理时间: " + (students.isEmpty() ? 0 : duration / students.size()) + "ms");
    }

    /**
     * 测试优化版本的性能
     */
    @Test
    public void testOptimizedJob4Performance() {
        System.out.println("=== 测试优化版本性能 ===");
        
        // 获取测试数据量
        WhereCondition wc = new WhereCondition();
        wc.andNotEquals("classStatus","5");
        List<Student_zone> students = student_zoneService.query(wc);
        System.out.println("测试数据量: " + students.size() + " 个学生");
        
        long startTime = System.currentTimeMillis();
        
        try {
            erpTaskJob.job4();
        } catch (Exception e) {
            System.err.println("优化版本执行失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("优化版本执行时间: " + duration + "ms");
        System.out.println("平均每个学生处理时间: " + (students.isEmpty() ? 0 : duration / students.size()) + "ms");
    }

    /**
     * 性能对比测试
     */
    @Test
    public void testPerformanceComparison() {
        System.out.println("=== 性能对比测试 ===");
        
        // 获取测试数据量
        WhereCondition wc = new WhereCondition();
        wc.andNotEquals("classStatus","5");
        List<Student_zone> students = student_zoneService.query(wc);
        System.out.println("测试数据量: " + students.size() + " 个学生");
        
        if (students.size() > 1000) {
            System.out.println("警告: 数据量较大，测试可能需要较长时间");
        }
        
        // 测试原始版本（仅在数据量不太大时）
        long originalTime = 0;
        if (students.size() <= 500) {
            System.out.println("\n--- 测试原始版本 ---");
            long startTime = System.currentTimeMillis();
            try {
                erpTaskJob.job4_original();
                originalTime = System.currentTimeMillis() - startTime;
                System.out.println("原始版本执行时间: " + originalTime + "ms");
            } catch (Exception e) {
                System.err.println("原始版本执行失败: " + e.getMessage());
            }
        } else {
            System.out.println("数据量过大，跳过原始版本测试");
        }
        
        // 测试优化版本
        System.out.println("\n--- 测试优化版本 ---");
        long startTime = System.currentTimeMillis();
        try {
            erpTaskJob.job4();
            long optimizedTime = System.currentTimeMillis() - startTime;
            System.out.println("优化版本执行时间: " + optimizedTime + "ms");
            
            if (originalTime > 0) {
                double improvement = ((double)(originalTime - optimizedTime) / originalTime) * 100;
                System.out.println("性能提升: " + String.format("%.2f", improvement) + "%");
                System.out.println("速度提升倍数: " + String.format("%.2f", (double)originalTime / optimizedTime) + "x");
            }
        } catch (Exception e) {
            System.err.println("优化版本执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 数据库连接池压力测试
     */
    @Test
    public void testDatabaseConnectionPoolStress() {
        System.out.println("=== 数据库连接池压力测试 ===");
        
        // 模拟多次并发执行
        int threadCount = 3;
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                System.out.println("线程 " + threadId + " 开始执行");
                long startTime = System.currentTimeMillis();
                
                try {
                    erpTaskJob.job4();
                    long duration = System.currentTimeMillis() - startTime;
                    System.out.println("线程 " + threadId + " 执行完成，耗时: " + duration + "ms");
                } catch (Exception e) {
                    System.err.println("线程 " + threadId + " 执行失败: " + e.getMessage());
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        System.out.println("所有线程执行完成");
    }
}
